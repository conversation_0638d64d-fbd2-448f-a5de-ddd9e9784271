<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱认证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #1976d2;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: #2196f3;
            color: #2196f3;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OneTab Plus - 邮箱认证测试</h1>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('login')">登录</div>
            <div class="tab" onclick="switchTab('register')">注册</div>
        </div>

        <!-- 登录表单 -->
        <div id="login-tab" class="tab-content active">
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">邮箱地址:</label>
                    <input type="email" id="loginEmail" required placeholder="请输入您的邮箱地址">
                </div>
                <div class="form-group">
                    <label for="loginPassword">密码:</label>
                    <input type="password" id="loginPassword" required placeholder="请输入您的密码">
                </div>
                <button type="submit" id="loginBtn">登录</button>
            </form>
        </div>

        <!-- 注册表单 -->
        <div id="register-tab" class="tab-content">
            <form id="registerForm">
                <div class="form-group">
                    <label for="registerEmail">邮箱地址:</label>
                    <input type="email" id="registerEmail" required placeholder="请输入您的邮箱地址">
                </div>
                <div class="form-group">
                    <label for="registerPassword">密码:</label>
                    <input type="password" id="registerPassword" required placeholder="请输入密码（至少6位）" minlength="6">
                </div>
                <div class="form-group">
                    <label for="confirmPassword">确认密码:</label>
                    <input type="password" id="confirmPassword" required placeholder="请再次输入密码" minlength="6">
                </div>
                <button type="submit" id="registerBtn">注册</button>
            </form>
        </div>

        <div id="result" class="result"></div>

        <div class="info" style="display: block; margin-top: 20px;">
            <strong>测试说明:</strong><br>
            1. 此页面用于测试OneTab Plus的邮箱认证功能<br>
            2. 确保已正确配置Supabase环境变量<br>
            3. 注册后需要检查邮箱进行验证<br>
            4. 第三方登录功能已被移除，只支持邮箱认证
        </div>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 添加active类到选中的标签
            event.target.classList.add('active');
            
            // 清除结果显示
            hideResult();
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        function hideResult() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
        }

        // 登录表单处理
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const loginBtn = document.getElementById('loginBtn');
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                showResult('正在尝试登录...', 'info');
                
                // 这里应该调用实际的登录API
                // 由于这是测试页面，我们模拟登录过程
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                showResult(`登录测试完成！邮箱: ${email}`, 'success');
            } catch (error) {
                showResult(`登录失败: ${error.message}`, 'error');
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });

        // 注册表单处理
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const registerBtn = document.getElementById('registerBtn');
            
            // 验证密码匹配
            if (password !== confirmPassword) {
                showResult('两次输入的密码不匹配', 'error');
                return;
            }
            
            registerBtn.disabled = true;
            registerBtn.textContent = '注册中...';
            
            try {
                showResult('正在尝试注册...', 'info');
                
                // 这里应该调用实际的注册API
                // 由于这是测试页面，我们模拟注册过程
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                showResult(`注册测试完成！邮箱: ${email}。请检查邮箱进行验证。`, 'success');
            } catch (error) {
                showResult(`注册失败: ${error.message}`, 'error');
            } finally {
                registerBtn.disabled = false;
                registerBtn.textContent = '注册';
            }
        });
    </script>
</body>
</html>
