# 安全配置指南

## 环境变量安全管理

### 1. 环境变量配置

本项目使用环境变量来管理敏感配置信息。请按照以下步骤正确配置：

#### 步骤1：复制配置模板
```bash
cp .env.example .env
```

#### 步骤2：配置 Supabase
编辑 `.env` 文件，填入您的 Supabase 项目配置：

```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-anon-key-here
```

### 2. 安全要求

#### ✅ 必须遵循的安全原则：

1. **只使用匿名密钥**
   - 仅使用 Supabase 的 `anon` 密钥
   - 绝不在前端代码中使用 `service_role` 密钥

2. **配置 RLS 策略**
   - 在 Supabase 中启用行级安全（Row Level Security）
   - 为所有表配置适当的 RLS 策略
   - 确保用户只能访问自己的数据

3. **环境变量保护**
   - `.env` 文件已在 `.gitignore` 中排除
   - 不要将真实配置提交到代码仓库
   - 定期轮换 API 密钥

#### ❌ 禁止的操作：

1. 在代码中硬编码 API 密钥
2. 将 `.env` 文件提交到版本控制
3. 在客户端暴露服务密钥
4. 在日志中输出完整的 API 密钥

### 3. 验证配置

项目启动时会自动验证配置：

- ✅ URL 格式验证
- ✅ 密钥格式验证  
- ✅ Supabase 域名验证
- ✅ 开发环境配置日志

### 4. 生产环境部署

在生产环境中：

1. 使用环境变量注入配置
2. 启用 HTTPS
3. 配置适当的 CSP 策略
4. 定期审查和轮换密钥

### 5. 安全检查清单

部署前请确认：

- [ ] 已配置正确的 Supabase RLS 策略
- [ ] 使用的是匿名密钥，不是服务密钥
- [ ] `.env` 文件未被提交到代码仓库
- [ ] 在 Supabase 控制台中配置了域名白名单
- [ ] 启用了适当的安全策略

### 6. 故障排除

#### 常见错误：

1. **"Supabase 配置缺失"**
   - 检查 `.env` 文件是否存在
   - 确认环境变量名称正确

2. **"无效的 Supabase URL 格式"**
   - 确认 URL 包含 `supabase.co` 域名
   - 检查 URL 格式是否正确

3. **"无效的匿名密钥格式"**
   - 确认密钥以 `eyJ` 开头（JWT 格式）
   - 检查密钥是否完整

### 7. 联系支持

如果遇到配置问题，请：

1. 检查 Supabase 控制台中的项目设置
2. 验证 RLS 策略配置
3. 查看浏览器控制台的错误信息
4. 参考 Supabase 官方文档

---

**重要提醒**：安全配置是保护用户数据的第一道防线，请严格遵循本指南的要求。
