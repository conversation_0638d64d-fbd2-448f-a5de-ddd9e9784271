// 手动同步测试
// 这个文件用于测试手动同步功能

// 测试步骤：
// 1. 登录账号
// 2. 点击同步按钮
// 3. 观察控制台输出，确认同步过程正常
// 4. 验证云端数据已成功同步到本地，并且本地数据与云端数据已智能合并去重

// 预期结果：
// - 同步过程中会显示"开始手动同步，以云端数据为准，智能合并去重..."
// - 同步完成后会显示"数据同步完成！云端数据已成功同步并与本地数据智能合并去重"
// - 本地数据应该包含所有云端数据，并且没有重复项
// - 如果本地和云端有相同URL的标签，应该只保留一个

// 测试用例：
// 1. 本地有标签A、B、C，云端有标签B、C、D
//    预期结果：本地应该有标签A、B、C、D，没有重复项
// 2. 本地有标签A(URL=x)、B，云端有标签C(URL=x)、D
//    预期结果：本地应该有标签B、C(URL=x)、D，因为A和C有相同的URL，优先保留云端的C
// 3. 本地和云端都有同一个标签组，但内容不同
//    预期结果：应该合并两个标签组，保留所有不重复的标签，优先使用云端的标签组名称和锁定状态
