@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  body {
    @apply bg-background text-on-background font-sans antialiased;
  }
}

@layer components {

  /* Material Design 按钮 */
  .btn-material {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200 ease-out bg-surface text-on-surface shadow-elevation-1 hover:shadow-elevation-2 active:shadow-elevation-1 uppercase text-sm tracking-wider;
  }

  .btn-material-primary {
    @apply bg-primary-500 text-on-primary hover:bg-primary-600 active:bg-primary-700;
  }

  .btn-material-secondary {
    @apply bg-secondary-500 text-on-secondary hover:bg-secondary-600 active:bg-secondary-700;
  }

  /* Material Design 卡片 */
  .card-material {
    @apply bg-surface rounded-lg shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 ease-out;
  }

  /* Material Design 输入框 */
  .input-material {
    @apply px-3 py-2 rounded-md border border-gray-300 bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }

  /* Material Design 浮动操作按钮 (FAB) */
  .fab {
    @apply w-14 h-14 rounded-full bg-primary-500 text-on-primary flex items-center justify-center shadow-elevation-2 hover:shadow-elevation-3 active:shadow-elevation-1 transition-all duration-200 ease-out;
  }

  .fab-small {
    @apply w-10 h-10;
  }

  /* 全宽布局容器 */
  .full-width-container {
    @apply w-full px-3 sm:px-4 md:px-6 lg:px-8;
  }

  /* 三栏布局优化 */
  .triple-column-layout {
    @apply grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3;
  }

  /* 双栏布局优化 */
  .double-column-layout {
    @apply grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-3;
  }

  /* 单栏布局优化 */
  .single-column-layout {
    @apply space-y-2 transition-all duration-200 ease-out;
  }

  /* 确保三栏布局在小屏幕上的可读性 */
  @media (max-width: 1024px) {
    .triple-column-layout {
      @apply grid-cols-1 md:grid-cols-2;
    }
  }

  /* 三栏布局的栏目容器 */
  .layout-column {
    @apply space-y-2 transition-all duration-200 ease-out;
  }

  /* 响应式间距优化 */
  .responsive-spacing {
    @apply space-y-2 sm:space-y-3 md:space-y-4;
  }

  /* 响应式网格间距 */
  .responsive-grid-gap {
    @apply gap-2 sm:gap-3 md:gap-4 lg:gap-5;
  }
}

/* 滚动条样式 - Material Design */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded hover:bg-gray-400;
}

/* 过渡动画 */
.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity 200ms ease-out;
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity 200ms ease-out;
}

/* Material Design 的平滑过渡 */
.transition-material {
  @apply transition-all duration-200 ease-out;
}

/* 拖拽项目 - 完全移除过渡效果 */
.draggable-item {
  position: relative;
}

/* 拖拽时的效果 - 完全模拟 OneTab 原版 */
.dragging {
  z-index: 10;
  opacity: 0.7;
  /* 拖拽时的透明度 */
  background-color: #f0f9ff !important;
  /* 浅蓝色背景，更稳定的视觉反馈 */
  outline: 1px dashed #3b82f6;
  /* 蓝色虚线边框 */
  outline-offset: 0;
}

/* 拖拽目标的效果 - 模拟 OneTab 原版 */
.drag-over {
  border-top: 2px solid #3b82f6;
  /* 更突出的蓝色指示线 */
  margin-top: 1px;
  /* 补偿边框宽度，避免元素跳动 */
}

/* 标签容器和标签项 - 移除所有过渡效果 */

/* 移除不再需要的脉冲动画 */

/* 移除未使用的 DnD Kit 特定样式 */

/* 模态框动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }

  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.2s ease-out forwards;
}

.animate-fadeOut {
  animation: fadeOut 0.2s ease-out forwards;
}