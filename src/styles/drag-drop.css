/* 拖曳相关样式 - 全新重构版 */

/* 基本过渡效果 */
.dnd-transition {
  transition: transform 0.2s ease, opacity 0.2s ease, margin 0.2s ease, box-shadow 0.2s ease;
}

/* 拖拽返回动画 - 当拖拽结束但未放置到有效目标时 */
.tab-drag-return {
  animation: tab-return 0.3s ease-out;
}

@keyframes tab-return {
  0% {
    transform: translateX(10px);
    opacity: 0.7;
  }

  50% {
    transform: translateX(-5px);
    opacity: 0.85;
  }

  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 标签页样式 */
.tab-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: white;
  border: 1px solid transparent;
  margin-bottom: 4px;
  cursor: move;
  position: relative;
}

.dark .tab-item {
  background-color: #1f2937;
  color: #e5e7eb;
}

.tab-item:hover {
  background-color: #f9fafb;
}

.dark .tab-item:hover {
  background-color: #374151;
}

/* 标签页拖拽中状态 */
.tab-item.dragging {
  opacity: 0.5;
  border-color: #3b82f6;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
  z-index: 999;
  background-color: #f0f7ff;
}

/* 标签组样式 */
.group-item {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  margin-bottom: 12px;
}

.dark .group-item {
  background-color: #1f2937;
  border-color: #374151;
  color: #e5e7eb;
}



/* 标签组拖拽中状态 */
.group-item.dragging {
  opacity: 0.7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

/* 拖拽覆盖层 - 标签页 */
.tab-drag-overlay {
  background-color: white;
  border-radius: 4px;
  border: 1px solid #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  padding: 8px 12px;
  display: flex;
  align-items: center;
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .tab-drag-overlay {
  background-color: #1f2937;
  color: #e5e7eb;
  border-color: #3b82f6;
}

/* 拖拽覆盖层 - 标签组 */
.group-drag-overlay {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 12px;
  width: 100%;
  max-width: 600px;
}

.dark .group-drag-overlay {
  background-color: #1f2937;
  border-color: #374151;
  color: #e5e7eb;
}

/* 拖拽目标样式 - 当鼠标悬停在可放置区域时 */
.drop-target {
  background-color: #f0f7ff;
  border-color: #bfdbfe;
}

.dark .drop-target {
  background-color: #1e3a8a;
  border-color: #3b82f6;
}

/* 拖拽指示器 - 显示当前拖拽位置 */
.drag-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #3b82f6;
  z-index: 1000;
}

.drag-indicator-top {
  top: -1px;
}

.drag-indicator-bottom {
  bottom: -1px;
}

/* 标签组内容区域 */
.tabs-container {
  transition: height 0.2s ease;
}

/* 标签组头部 */
.group-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  cursor: move;
}

.dark .group-header {
  background-color: #111827;
  border-color: #374151;
  color: #e5e7eb;
}

/* 兼容旧样式 */
.tab-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: white;
  border: 1px solid transparent;
  margin-bottom: 4px;
  cursor: move;
  position: relative;
  transition: transform 0.15s ease, opacity 0.15s ease;
}

.dark .tab-item {
  background-color: #1f2937;
  color: #e5e7eb;
}

.tab-item:hover {
  background-color: #f5f5f5;
}

.dark .tab-item:hover {
  background-color: #374151;
}

.tab-item.dragging {
  opacity: 0.5;
  border-color: #3b82f6;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
  z-index: 999;
  background-color: #f0f7ff;
}

.dark .tab-item.dragging {
  background-color: #1e40af;
  border-color: #3b82f6;
}

.drag-overlay {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .drag-overlay {
  background-color: #1f2937;
  border-color: #374151;
  color: #e5e7eb;
}

.group-item {
  transition: transform 0.15s ease, opacity 0.15s ease;
}

.group-item.dragging {
  opacity: 0.5;
  border-color: #ccc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.group-overlay {
  padding: 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .group-overlay {
  background-color: #1f2937;
  border-color: #374151;
  color: #e5e7eb;
}

.tab-overlay {
  background-color: #f0f7ff;
  border-color: #3b82f6;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

.dark .tab-overlay {
  background-color: #1e40af;
  border-color: #3b82f6;
}

.insert-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #3b82f6;
  z-index: 1000;
  animation: pulse 1.5s infinite;
  border-radius: 1.5px;
  box-shadow: 0 0 4px rgba(59, 130, 246, 0.5);
}

@keyframes pulse {
  0% {
    opacity: 0.7;
    transform: scaleY(1);
  }

  50% {
    opacity: 1;
    transform: scaleY(1.3);
  }

  100% {
    opacity: 0.7;
    transform: scaleY(1);
  }
}

.insert-indicator-top {
  top: -2px;
}

.insert-indicator-bottom {
  bottom: -2px;
}

.drop-target {
  background-color: #f0f7ff;
  border-color: #bfdbfe;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
  transition: all 0.2s cubic-bezier(0.2, 0, 0, 1);
}