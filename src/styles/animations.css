/* 动画效果样式 */

/* 标签组折叠/展开动画 */
.tab-group-content {
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.tab-group-content.collapsed {
  max-height: 0;
}

.tab-group-content.expanded {
  max-height: 1000px; /* 足够大的值，确保内容能完全展开 */
}

/* 标签组折叠/展开图标动画 */
.expand-icon {
  transition: transform 0.3s ease;
}

.expand-icon.collapsed {
  transform: rotate(-90deg);
}

.expand-icon.expanded {
  transform: rotate(0deg);
}

/* 拖拽动画效果 */
.tab-drag-animation {
  transition: transform 0.15s ease, opacity 0.15s ease, box-shadow 0.15s ease;
}

.tab-drag-animation.dragging {
  transform: scale(1.02);
  opacity: 0.7;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* 放置目标高亮动画 */
.drop-target-animation {
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

.drop-target-animation.active {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.5);
}

/* 深色模式下的放置目标高亮 */
.dark .drop-target-animation.active {
  background-color: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.6);
}

/* 标签组拖拽动画 */
.group-drag-animation {
  transition: transform 0.15s ease, opacity 0.15s ease, box-shadow 0.15s ease;
}

.group-drag-animation.dragging {
  transform: scale(1.01);
  opacity: 0.8;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* 搜索结果高亮动画 */
@keyframes highlight-pulse {
  0% {
    background-color: rgba(59, 130, 246, 0.2);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.3);
  }
  100% {
    background-color: rgba(59, 130, 246, 0.2);
  }
}

.search-highlight {
  background-color: rgba(59, 130, 246, 0.2);
  border-radius: 2px;
  padding: 0 2px;
  animation: highlight-pulse 1.5s infinite;
}

.dark .search-highlight {
  background-color: rgba(59, 130, 246, 0.3);
}

/* 标签组操作按钮动画 */
.group-action-button {
  opacity: 0.5;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.group-action-button:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* 标签页操作按钮动画 */
.tab-action-button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tab-item:hover .tab-action-button {
  opacity: 1;
}

/* 标签组头部悬停效果 */
.group-header {
  transition: background-color 0.2s ease;
}

.group-header:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .group-header:hover {
  background-color: rgba(255, 255, 255, 0.05);
}
