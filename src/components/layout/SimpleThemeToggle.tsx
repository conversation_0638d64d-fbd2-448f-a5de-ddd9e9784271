import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { saveSettings } from '@/store/slices/settingsSlice';

export const SimpleThemeToggle: React.FC = () => {
  const dispatch = useAppDispatch();
  const { themeMode, setThemeMode } = useTheme();
  const settings = useAppSelector(state => state.settings);

  const toggleTheme = () => {
    // 在浅色和深色模式之间切换
    const newMode = themeMode === 'light' ? 'dark' : 'light';
    setThemeMode(newMode);

    // 保存设置到存储
    dispatch(saveSettings({ ...settings, themeMode: newMode }))
      .then(() => {
        console.log('主题设置已保存:', newMode);
      })
      .catch(error => {
        console.error('保存主题设置失败:', error);
      });
  };

  // 获取当前主题图标
  const getThemeIcon = () => {
    if (themeMode === 'dark') {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
        </svg>
      );
    } else {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
      );
    }
  };

  return (
    <button
      onClick={toggleTheme}
      className="p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-gray-600 dark:text-gray-300 flex items-center justify-center"
      title={themeMode === 'light' ? "切换到深色模式" : "切换到浅色模式"}
    >
      {getThemeIcon()}
    </button>
  );
};

export default SimpleThemeToggle;
